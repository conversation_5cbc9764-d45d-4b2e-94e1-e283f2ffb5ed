<!DOCTYPE html>
<html lang="id">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Informasi Desa - Desa Wisata Klabili</title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="css/bootstrap/bootstrap.css" />
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/custom.css" />
    <style>
      .section-title {
        position: relative;
        font-size: 3rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 30px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
      }

      .section-title::after {
        content: "";
        position: absolute;
        bottom: -15px;
        left: 50%;
        transform: translateX(-50%);
        width: 100px;
        height: 6px;

        border-radius: 3px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .section-subtitle {
        font-size: 1.2rem;
        color: #6c757d;
        margin-bottom: 50px;
        font-weight: 400;
      }

      .card-hover-lift {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        border: none;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      }

      .card-hover-lift:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
      }

      .decorative-line {
        position: relative;
        margin: 60px 0;
        height: 3px;
        background: linear-gradient(
          90deg,
          transparent,
          #007bff,
          #28a745,
          #ffc107,
          #dc3545,
          transparent
        );
        border-radius: 2px;
      }

      .decorative-line::before {
        content: "❋";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        padding: 0 15px;
        color: #6c757d;
        font-size: 18px;
        font-weight: bold;
      }

      .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 40px;
        margin: 50px 0;
      }

      .info-card {
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.95),
          rgba(248, 249, 250, 0.95)
        );
        border: none;
        border-radius: 25px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08);
        transition: all 0.4s ease;
        backdrop-filter: blur(15px);
        position: relative;
        overflow: hidden;
        padding: 30px;
      }

      .info-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 6px;    
        border-radius: 25px 25px 0 0;
      }

      .info-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
      }

      .icon-wrapper {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 25px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        transition: all 0.3s ease;
      }

      .info-card:hover .icon-wrapper {
        transform: scale(1.1) rotate(5deg);
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
      }

      .contact-item {
        background: white;
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 25px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        border-left: 5px solid #007bff;
      }

      .contact-item:hover {
        transform: translateX(10px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
        border-left-color: #28a745;
      }

      .contact-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-size: 24px;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
      }

      /* Modern Contact Cards */
      .contact-card-modern {
        background: white;
        border-radius: 20px;
        padding: 0;
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
        transition: all 0.4s ease;
        border: 2px solid transparent;
        overflow: hidden;
        height: 100%;
      }

      .contact-card-modern:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        border-color: rgba(25, 135, 84, 0.3);
      }

      .contact-card-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 2rem 1.5rem 1rem;
        text-align: center;
        position: relative;
      }

      .contact-icon-modern {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        font-size: 1.8rem;
        color: white;
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
      }

      .contact-icon-modern.phone {
        background: linear-gradient(135deg, #198754, #20c997);
      }

      .contact-icon-modern.whatsapp {
        background: linear-gradient(135deg, #25d366, #128c7e);
      }

      .contact-icon-modern.email {
        background: linear-gradient(135deg, #0d6efd, #6610f2);
      }

      .contact-card-modern:hover .contact-icon-modern {
        transform: scale(1.1) rotate(5deg);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      }

      .contact-title {
        font-size: 1.1rem;
        font-weight: 700;
        color: #198754;
        margin: 0;
      }

      .contact-card-body {
        padding: 1.5rem;
        text-align: center;
      }

      .contact-value {
        font-size: 1.1rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
      }

      .contact-status {
        display: inline-flex;
        align-items: center;
        font-size: 0.85rem;
        font-weight: 500;
        padding: 0.3rem 0.8rem;
        border-radius: 15px;
        margin-bottom: 1rem;
      }

      .contact-status.available {
        background: rgba(25, 135, 84, 0.1);
        color: #198754;
      }

      .contact-status.fast {
        background: rgba(37, 211, 102, 0.1);
        color: #25d366;
      }

      .contact-status.normal {
        background: rgba(13, 110, 253, 0.1);
        color: #0d6efd;
      }

      .contact-status i {
        font-size: 0.7rem;
        animation: pulse 2s infinite;
      }

      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
      }

      /* Social Media Section */
      .social-media-section {
        background: white;
        border-radius: 25px;
        padding: 2.5rem;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
        border: 2px solid rgba(25, 135, 84, 0.1);
      }

      .social-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        border: 2px solid transparent;
        height: 100%;
      }

      .social-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
      }

      .social-card.instagram {
        border-color: rgba(225, 48, 108, 0.2);
      }

      .social-card.instagram:hover {
        border-color: #e1306c;
      }

      .social-card.facebook {
        border-color: rgba(24, 119, 242, 0.2);
      }

      .social-card.facebook:hover {
        border-color: #1877f2;
      }

      .social-card-content {
        display: flex;
        align-items: center;
        gap: 1rem;
      }

      .social-icon-wrapper {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        flex-shrink: 0;
      }

      .social-card.instagram .social-icon-wrapper {
        background: linear-gradient(135deg, #e1306c, #fd1d1d, #fcb045);
      }

      .social-card.facebook .social-icon-wrapper {
        background: linear-gradient(135deg, #1877f2, #42a5f5);
      }

      .social-info {
        flex: 1;
      }

      .social-platform {
        font-weight: 700;
        color: #198754;
        margin-bottom: 0.25rem;
        font-size: 1rem;
      }

      .social-handle {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.25rem;
        font-size: 0.95rem;
      }

      .social-description {
        color: #6c757d;
        font-size: 0.8rem;
        margin: 0;
      }

      .social-action {
        flex-shrink: 0;
      }

      .btn-instagram {
        background: linear-gradient(135deg, #e1306c, #fd1d1d);
        border: none;
        color: white;
      }

      .btn-instagram:hover {
        background: linear-gradient(135deg, #fd1d1d, #e1306c);
        color: white;
        transform: translateY(-2px);
      }

      .btn-facebook {
        background: linear-gradient(135deg, #1877f2, #42a5f5);
        border: none;
        color: white;
      }

      .btn-facebook:hover {
        background: linear-gradient(135deg, #42a5f5, #1877f2);
        color: white;
        transform: translateY(-2px);
      }

      .map-container {
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        height: 400px;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6c757d;
        font-size: 18px;
        font-weight: 500;
      }

      .map-container-enhanced {
        min-height: 400px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .map-placeholder {
        width: 100%;
      }

      .map-icon-wrapper {
        animation: float 3s ease-in-out infinite;
      }

      .map-header {
        background: linear-gradient(135deg, #198754 0%, #20c997 100%) !important;
      }

      .address-info .address-item {
        position: relative;
      }

      .altitude-info {
        border-left: 4px solid #198754;
        transition: all 0.3s ease;
      }

      .altitude-info:hover {
        background: #e8f5e8 !important;
        transform: translateX(5px);
      }

      .transport-info {
        border-left: 4px solid #0d6efd;
        transition: all 0.3s ease;
      }

      .transport-info:hover {
        background: #e7f3ff !important;
        transform: translateY(-2px);
      }

      .transport-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #e7f3ff, #cce7ff);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
      }

      .nav-pills .nav-link {
        border: 2px solid transparent;
        font-weight: 600;
        transition: all 0.3s ease;
      }

      .nav-pills .nav-link:hover {
        background: rgba(25, 135, 84, 0.1);
        border-color: rgba(25, 135, 84, 0.3);
        transform: translateY(-2px);
      }

      .nav-pills .nav-link.active {
        background: linear-gradient(135deg, #198754, #20c997);
        border-color: #198754;
        box-shadow: 0 4px 15px rgba(25, 135, 84, 0.3);
      }

      .map-features .badge {
        transition: all 0.3s ease;
      }

      .map-features .badge:hover {
        background: linear-gradient(135deg, #198754, #20c997) !important;
        color: white !important;
        transform: translateY(-2px);
      }

      @keyframes float {
        0%, 100% {
          transform: translateY(0px);
        }
        50% {
          transform: translateY(-10px);
        }
      }

      /* Enhanced Hero Section for Info Page */
      .hero-section-info {
        background: linear-gradient(135deg, #198754 0%, #20c997 50%, #0d6efd 100%);
        position: relative;
        min-height: 70vh;
        display: flex;
        align-items: center;
      }

      .hero-bg-info {
        background: rgba(0, 0, 0, 0.3);
        z-index: 1;
      }

      .hero-section-info .container {
        position: relative;
        z-index: 2;
      }

      .hero-section-info .hero-badge .badge {
        font-size: 0.9rem;
        font-weight: 600;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        animation: fadeInUp 0.8s ease-out;
      }

      .hero-section-info .hero-title {
        font-size: 3.5rem;
        line-height: 1.2;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        animation: fadeInUp 0.8s ease-out 0.2s both;
      }

      .hero-section-info .text-gradient {
        background: linear-gradient(45deg, #ffd700, #ffeb3b, #fff);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-weight: 800;
      }

      .hero-section-info .hero-subtitle {
        max-width: 800px;
        margin: 0 auto;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        animation: fadeInUp 0.8s ease-out 0.4s both;
      }

      .hero-section-info .hero-stats {
        animation: fadeInUp 0.8s ease-out 0.6s both;
      }

      .hero-section-info .stat-item {
        text-align: center;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
        min-width: 100px;
      }

      .hero-section-info .stat-item:hover {
        transform: translateY(-5px);
        background: rgba(255, 255, 255, 0.2);
      }

      .hero-section-info .stat-number {
        font-size: 2rem;
        font-weight: 800;
        color: #ffd700;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
      }

      .hero-section-info .stat-label {
        font-size: 0.9rem;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.9);
      }

      /* Hero Decorative Elements */
      .hero-section-info .hero-decoration {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        animation: float 6s ease-in-out infinite;
      }

      .hero-section-info .hero-decoration-1 {
        width: 100px;
        height: 100px;
        top: 10%;
        left: 10%;
        animation-delay: 0s;
      }

      .hero-section-info .hero-decoration-2 {
        width: 150px;
        height: 150px;
        top: 60%;
        right: 15%;
        animation-delay: 2s;
      }

      .hero-section-info .hero-decoration-3 {
        width: 80px;
        height: 80px;
        bottom: 20%;
        left: 20%;
        animation-delay: 4s;
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .transport-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        border-top: 4px solid #007bff;
      }

      .transport-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
      }

      .nav-tabs .nav-link {
        border: none;
        border-radius: 25px 25px 0 0;
        padding: 15px 25px;
        font-weight: 600;
        color: #6c757d;
        background: #f8f9fa;
        margin-right: 5px;
        transition: all 0.3s ease;
      }

      .nav-tabs .nav-link.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
      }

      .tab-content {
        background: white;
        border-radius: 0 15px 15px 15px;
        padding: 30px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
      } 

      .weather-card {
        background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        color: white;
        border-radius: 20px;
        padding: 30px;
        text-align: center;
        box-shadow: 0 15px 35px rgba(116, 185, 255, 0.3);
      }

      .weather-icon {
        font-size: 4rem;
        margin-bottom: 20px;
        text-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      }

      .tips-card {
        background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
        color: white;
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 15px 35px rgba(253, 121, 168, 0.3);
      }

      .tips-item {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 15px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .emergency-card {
        background: linear-gradient(135deg, #ff7675 0%, #d63031 100%);
        color: white;
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 15px 35px rgba(255, 118, 117, 0.3);
      }

      .emergency-item {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 15px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
      }

      .emergency-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        font-size: 20px;
      }

      /* Enhanced Info Cards */
      .info-card-enhanced {
        background: white;
        border-radius: 25px;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
        transition: all 0.4s ease;
        border: 2px solid transparent;
        overflow: hidden;
        height: 100%;
        position: relative;
      }

      .info-card-enhanced:hover {
        transform: translateY(-8px);
        box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15);
      }

      .info-card-enhanced.weather {
        border-color: rgba(116, 185, 255, 0.2);
      }

      .info-card-enhanced.weather:hover {
        border-color: #74b9ff;
      }

      .info-card-enhanced.tips {
        border-color: rgba(253, 121, 168, 0.2);
      }

      .info-card-enhanced.tips:hover {
        border-color: #fd79a8;
      }

      .info-card-enhanced.emergency {
        border-color: rgba(255, 118, 117, 0.2);
      }

      .info-card-enhanced.emergency:hover {
        border-color: #ff7675;
      }

      .info-card-header {
        padding: 2rem 1.5rem 1rem;
        text-align: center;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      }

      .info-icon-wrapper {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        font-size: 2rem;
        color: white;
        transition: all 0.4s ease;
        position: relative;
      }

      .info-icon-wrapper.weather-icon {
        background: linear-gradient(135deg, #74b9ff, #0984e3);
      }

      .info-icon-wrapper.tips-icon {
        background: linear-gradient(135deg, #fd79a8, #e84393);
      }

      .info-icon-wrapper.emergency-icon {
        background: linear-gradient(135deg, #ff7675, #d63031);
      }

      .info-card-enhanced:hover .info-icon-wrapper {
        transform: scale(1.1) rotate(5deg);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
      }

      .info-card-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: #198754;
        margin: 0;
      }

      .info-card-body {
        padding: 1.5rem;
      }

      /* Weather Details */
      .weather-details {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-bottom: 1.5rem;
      }

      .weather-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: 10px;
        border-left: 4px solid #74b9ff;
      }

      .weather-label {
        font-weight: 600;
        color: #495057;
        display: flex;
        align-items: center;
      }

      .weather-value {
        font-weight: 700;
        color: #198754;
        font-size: 1.1rem;
      }

      .weather-note {
        background: rgba(116, 185, 255, 0.1);
        color: #0984e3;
        padding: 0.75rem;
        border-radius: 10px;
        font-size: 0.9rem;
        font-weight: 500;
        text-align: center;
      }

      /* Tips List */
      .tips-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;
      }

      .tip-item {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 12px;
        border-left: 4px solid #fd79a8;
        transition: all 0.3s ease;
      }

      .tip-item:hover {
        background: rgba(253, 121, 168, 0.05);
        transform: translateX(5px);
      }

      .tip-icon {
        width: 35px;
        height: 35px;
        background: linear-gradient(135deg, #fd79a8, #e84393);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 0.9rem;
        flex-shrink: 0;
      }

      .tip-content strong {
        color: #198754;
        font-weight: 700;
        display: block;
        margin-bottom: 0.25rem;
      }

      .tip-content p {
        margin: 0;
        color: #6c757d;
        font-size: 0.9rem;
        line-height: 1.4;
      }

      /* Emergency Contacts */
      .emergency-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-bottom: 1.5rem;
      }

      .emergency-contact {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 12px;
        border-left: 4px solid #ff7675;
        transition: all 0.3s ease;
      }

      .emergency-contact:hover {
        background: rgba(255, 118, 117, 0.05);
        transform: translateX(5px);
      }

      .emergency-contact-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #ff7675, #d63031);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1rem;
        flex-shrink: 0;
      }

      .emergency-contact-info h6 {
        color: #198754;
        font-weight: 700;
        margin-bottom: 0.25rem;
        font-size: 1rem;
      }

      .emergency-contact-info p {
        margin-bottom: 0.25rem;
        color: #495057;
        font-size: 0.9rem;
      }

      .emergency-number {
        font-weight: 700 !important;
        color: #d63031 !important;
        font-size: 1rem !important;
      }

      .availability {
        background: rgba(25, 135, 84, 0.1);
        color: #198754;
        padding: 0.2rem 0.6rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
      }

      .emergency-note {
        background: rgba(255, 118, 117, 0.1);
        color: #d63031;
        padding: 0.75rem;
        border-radius: 10px;
        font-size: 0.9rem;
        font-weight: 500;
        text-align: center;
      }

      /* Additional Tips Section */
      .additional-tips-section {
        background: white;
        border-radius: 25px;
        padding: 2.5rem;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
        border: 2px solid rgba(25, 135, 84, 0.1);
      }

      .tip-highlight-card {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 20px;
        padding: 2rem;
        text-align: center;
        border: 2px solid rgba(25, 135, 84, 0.1);
        transition: all 0.3s ease;
        height: 100%;
      }

      .tip-highlight-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
        border-color: rgba(25, 135, 84, 0.3);
      }

      .tip-highlight-header h5 {
        color: #198754;
        font-weight: 700;
        margin-bottom: 1.5rem;
      }

      .tip-list {
        list-style: none;
        padding: 0;
        margin: 0;
        text-align: left;
      }

      .tip-list li {
        padding: 0.75rem 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        color: #495057;
        font-size: 0.9rem;
        line-height: 1.5;
        position: relative;
        padding-left: 1.5rem;
      }

      .tip-list li:last-child {
        border-bottom: none;
      }

      .tip-list li::before {
        content: "✓";
        position: absolute;
        left: 0;
        color: #198754;
        font-weight: bold;
      }

      @media (max-width: 768px) {
        .section-title {
          font-size: 2rem;
        }

        .info-grid {
          grid-template-columns: 1fr;
          gap: 20px;
        }

        .contact-item:hover {
          transform: none;
        }

        /* Hero Section Mobile */
        .hero-section-info {
          min-height: 60vh;
        }

        .hero-section-info .hero-title {
          font-size: 2.5rem;
        }

        .hero-section-info .hero-subtitle {
          font-size: 1.1rem;
        }

        .hero-section-info .hero-stats {
          gap: 1rem !important;
        }

        .hero-section-info .stat-item {
          padding: 0.75rem;
          min-width: 80px;
        }

        .hero-section-info .stat-number {
          font-size: 1.5rem;
        }

        /* Contact Cards Mobile */
        .contact-card-modern:hover {
          transform: translateY(-4px);
        }

        .contact-card-header {
          padding: 1.5rem 1rem 0.75rem;
        }

        .contact-icon-modern {
          width: 60px;
          height: 60px;
          font-size: 1.5rem;
        }

        .contact-card-body {
          padding: 1rem;
        }

        .social-media-section {
          padding: 1.5rem;
        }

        .social-card-content {
          flex-direction: column;
          text-align: center;
          gap: 0.75rem;
        }

        /* Info Cards Mobile */
        .info-card-enhanced:hover {
          transform: translateY(-4px);
        }

        .info-card-header {
          padding: 1.5rem 1rem 0.75rem;
        }

        .info-icon-wrapper {
          width: 60px;
          height: 60px;
          font-size: 1.5rem;
        }

        .info-card-body {
          padding: 1rem;
        }

        .weather-item {
          flex-direction: column;
          text-align: center;
          gap: 0.5rem;
        }

        .tip-item {
          flex-direction: column;
          text-align: center;
          gap: 0.75rem;
        }

        .emergency-contact {
          flex-direction: column;
          text-align: center;
          gap: 0.75rem;
        }

        .additional-tips-section {
          padding: 1.5rem;
        }

        .tip-highlight-card {
          padding: 1.5rem;
        }

        /* Transport Section Mobile */
        .nav-pills .nav-link {
          font-size: 0.85rem;
          padding: 0.5rem 0.75rem;
        }

        .transport-info {
          padding: 1rem;
        }

        .transport-icon {
          width: 35px;
          height: 35px;
          font-size: 1rem;
        }
      }

      @media (max-width: 576px) {
        .hero-section-info {
          min-height: 50vh;
        }

        .hero-section-info .hero-title {
          font-size: 2rem;
        }

        .hero-section-info .hero-subtitle {
          font-size: 1rem;
        }

        .hero-section-info .stat-item {
          padding: 0.5rem;
          min-width: 70px;
        }

        .hero-section-info .stat-number {
          font-size: 1.25rem;
        }

        .hero-section-info .stat-label {
          font-size: 0.8rem;
        }

        .contact-icon-modern {
          width: 50px;
          height: 50px;
          font-size: 1.25rem;
        }

        .info-icon-wrapper {
          width: 50px;
          height: 50px;
          font-size: 1.25rem;
        }

        .social-icon-wrapper {
          width: 40px;
          height: 40px;
          font-size: 1.25rem;
        }

        .nav-pills .nav-link {
          font-size: 0.8rem;
          padding: 0.4rem 0.6rem;
        }

        .nav-pills .nav-link i {
          display: none;
        }

        .tip-list li {
          font-size: 0.85rem;
          padding: 0.5rem 0;
        }

        .map-icon-wrapper {
          font-size: 3rem !important;
        }

        .map-features {
          flex-direction: column;
          gap: 0.5rem !important;
        }

        .map-features .badge {
          font-size: 0.75rem;
          padding: 0.4rem 0.8rem;
        }
      }

      @media (max-width: 480px) {
        .section-title {
          font-size: 1.75rem;
        }

        .section-subtitle {
          font-size: 1rem;
        }

        .hero-section-info .hero-title {
          font-size: 1.75rem;
        }

        .contact-card-modern,
        .info-card-enhanced {
          margin-bottom: 1rem;
        }

        .weather-item,
        .tip-item,
        .emergency-contact {
          padding: 0.75rem;
        }

        .tip-highlight-card {
          padding: 1rem;
        }

        .additional-tips-section {
          padding: 1rem;
        }

        .social-media-section {
          padding: 1rem;
        }
      }
    </style>
  </head>

  <body>
    <nav class="navbar navbar-expand-lg bg-body-tertiary">
      <div class="container-fluid">
        <!-- Left side: Logo -->
        <div class="navbar-brand-container" style="flex: 1">
          <a class="navbar-brand" href="index.html"
            ><img
              src="img/Logo_Desa_Wisata_Klabili-removebg-preview.png"
              alt=""
              width="100px"
          /></a>
        </div>

        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarSupportedContent"
          aria-controls="navbarSupportedContent"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <span class="navbar-toggler-icon"></span>
        </button>

        <!-- Language Toggle Switch - Outside collapsible menu -->
        <div class="navbar-nav d-flex align-items-center">
          <div
            class="nav-item d-flex align-items-center language-toggle-container en-active"
            id="languageToggleContainer"
          >
            <svg
              class="flag-img flag-en inactive"
              width="52"
              height="36"
              viewBox="0 0 20 15"
              xmlns="http://www.w3.org/2000/svg"
              onclick="setLanguage('en')"
            >
              <rect width="20" height="15" fill="#012169" />
              <path d="M0 0L20 15M20 0L0 15" stroke="#fff" stroke-width="2" />
              <path
                d="M0 0L20 15M20 0L0 15"
                stroke="#C8102E"
                stroke-width="1"
              />
              <path d="M10 0V15M0 7.5H20" stroke="#fff" stroke-width="3" />
              <path d="M10 0V15M0 7.5H20" stroke="#C8102E" stroke-width="2" />
            </svg>
            <span class="language-toggle-dot"></span>
            <svg
              class="flag-img flag-id active"
              width="32"
              height="22"
              viewBox="0 0 20 15"
              xmlns="http://www.w3.org/2000/svg"
              onclick="setLanguage('id')"
            >
              <rect width="20" height="7.5" fill="#FF0000" />
              <rect y="7.5" width="20" height="7.5" fill="#FFFFFF" />
            </svg>
          </div>
        </div>

        <div class="collapse navbar-collapse" id="navbarSupportedContent">
          <!-- Center: Navigation items -->
          <ul
            class="navbar-nav mx-auto mb-2 mb-lg-0 justify-content-center"
            style="flex: 1"
          >
            <li class="nav-item">
              <button
                class="btn nav-btn"
                type="button"
                data-nav-url="index.html"
              >
                Beranda
              </button>
            </li>

            <li class="nav-item dropdown">
              <button
                class="btn nav-btn dropdown-toggle"
                type="button"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                Tentang Kami
              </button>
              <ul class="dropdown-menu">
                <li>
                  <button
                    class="dropdown-item"
                    type="button"
                    data-nav-url="tentang_kami.html#profil-desa"
                  >
                    Profil Desa
                  </button>
                </li>
                <li>
                  <button
                    class="dropdown-item"
                    type="button"
                    data-nav-url="tentang_kami.html#struktur-desa"
                  >
                    Struktur Desa
                  </button>
                </li>
              </ul>
            </li>

            <li class="nav-item dropdown">
              <button
                class="btn nav-btn dropdown-toggle"
                type="button"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                Fasilitas
              </button>
              <ul class="dropdown-menu">
                <li>
                  <button
                    class="dropdown-item"
                    type="button"
                    data-nav-url="fasilitas.html#fasilitas-umum-desa"
                  >
                    Fasilitas Umum Desa
                  </button>
                </li>
              </ul>
            </li>

            <li class="nav-item dropdown">
              <button
                class="btn nav-btn dropdown-toggle"
                type="button"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                Wisata & Budaya
              </button>
              <ul class="dropdown-menu">
                <li>
                  <button
                    class="dropdown-item"
                    type="button"
                    data-nav-url="wisata_budaya.html#spot-wisata"
                  >
                    Spot Wisata
                  </button>
                </li>
                <li>
                  <button
                    class="dropdown-item"
                    type="button"
                    data-nav-url="wisata_budaya.html#budaya-lokal"
                  >
                    Budaya Lokal
                  </button>
                </li>
                <li>
                  <button
                    class="dropdown-item"
                    type="button"
                    data-nav-url="wisata_budaya.html#acara-tahunan"
                  >
                    Acara Tahunan
                  </button>
                </li>
                <li>
                  <button
                    class="dropdown-item"
                    type="button"
                    data-nav-url="wisata_budaya.html#kerajinan-tangan"
                  >
                    Kerajinan Tangan
                  </button>
                </li>
                <li>
                  <button
                    class="dropdown-item"
                    type="button"
                    data-nav-url="wisata_budaya.html#kuliner-lokal"
                  >
                    Kuliner Lokal
                  </button>
                </li>
                <li>
                  <hr class="dropdown-divider" />
                </li>
                <li>
                  <button
                    class="dropdown-item"
                    type="button"
                    data-nav-url="wisata_budaya.html#souvenir"
                  >
                    Souvenir
                  </button>
                </li>
              </ul>
            </li>

            <li class="nav-item dropdown">
              <button
                class="btn nav-btn dropdown-toggle active"
                type="button"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                Informasi Desa
              </button>
              <ul class="dropdown-menu">
                <li>
                  <button
                    class="dropdown-item"
                    type="button"
                    data-nav-url="informasi.html#lokasi-peta"
                  >
                    Lokasi & Peta
                  </button>
                </li>
                <li>
                  <button
                    class="dropdown-item"
                    type="button"
                    data-nav-url="informasi.html#kontak"
                  >
                    Kontak
                  </button>
                </li>
              </ul>
            </li>

            <li class="nav-item dropdown">
              <button
                class="btn nav-btn dropdown-toggle"
                type="button"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                Galeri
              </button>
              <ul class="dropdown-menu">
                <li>
                  <button
                    class="dropdown-item"
                    type="button"
                    data-nav-url="galeri.html#dokumentasi-acara"
                  >
                    Dokumentasi Acara
                  </button>
                </li>
                <li>
                  <button
                    class="dropdown-item"
                    type="button"
                    data-nav-url="galeri.html#kegiatan-masyarakat"
                  >
                    Kegiatan Masyarakat
                  </button>
                </li>
              </ul>
            </li>
          </ul>
        </div>
      </div>
    </nav>

    <!-- Enhanced Page Header -->
    <section class="hero-section-info position-relative overflow-hidden">
      <div class="hero-bg-info position-absolute w-100 h-100"></div>
      <div class="hero-decoration hero-decoration-1"></div>
      <div class="hero-decoration hero-decoration-2"></div>
      <div class="hero-decoration hero-decoration-3"></div>

      <div class="container py-5">
        <div class="row align-items-center min-vh-50">
          <div class="col-lg-8 mx-auto text-center text-white">
            <div class="hero-badge mb-4">
              <span class="badge bg-light text-success px-4 py-2 rounded-pill fs-6">
                <i class="fas fa-info-circle me-2"></i>Panduan Lengkap
              </span>
            </div>
            <h1 class="hero-title display-3 mb-4 fw-bold">
              Informasi <span class="text-gradient">Desa Wisata</span>
            </h1>
            <p class="hero-subtitle lead fs-4 mb-4">
              Panduan lengkap tentang lokasi, kontak, dan tips perjalanan untuk pengalaman terbaik di Desa Wisata Klabili
            </p>
            <div class="hero-stats d-flex justify-content-center gap-4 flex-wrap">
              <div class="stat-item">
                <div class="stat-number">800m</div>
                <div class="stat-label">Ketinggian</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">24/7</div>
                <div class="stat-label">Bantuan</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">3+</div>
                <div class="stat-label">Rute Akses</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <div class="container mt-4">
      <!-- Page Content -->
      <!-- Location & Map Section -->
      <section id="lokasi-peta" class="py-5" style="background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);">
        <div class="container">
          <div class="row">
            <div class="col-12 text-center mb-5">
              <div class="section-header">
                <span class="badge bg-success-subtle text-success px-3 py-2 rounded-pill mb-3">
                  <i class="fas fa-map-marker-alt me-2"></i>Lokasi & Akses
                </span>
                <h2 class="section-title">Lokasi & Peta</h2>
                <p class="section-subtitle">Temukan jalan ke Desa Wisata Klabili dengan mudah</p>
              </div>
            </div>
          </div>

          <div class="row g-4 mb-5">
            <!-- Enhanced Address Card -->
            <div class="col-lg-6">
              <div class="wisata-card h-100">
                <div class="card-body p-4 text-center">
                  <div class="icon-wrapper mb-4">
                    <i class="fas fa-map-marker-alt text-white fa-2x"></i>
                  </div>
                  <h4 class="card-title mb-4">Alamat Desa</h4>
                  <div class="address-info">
                    <div class="address-item mb-3">
                      <h5 class="text-success fw-bold mb-2">Desa Klabili</h5>
                      <p class="text-muted mb-1">Kecamatan Selemkai</p>
                      <p class="text-muted mb-1">Kabupaten Tambrauw</p>
                      <p class="text-muted mb-1">Provinsi Papua Barat Daya</p>
                      <p class="text-muted">Indonesia</p>
                    </div>
                    <div class="altitude-info bg-light rounded-3 p-3">
                      <i class="fas fa-mountain text-success me-2"></i>
                      <small class="text-muted">
                        Terletak pada ketinggian <strong>800 meter</strong> di atas permukaan laut
                      </small>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Enhanced Transport Options -->
            <div class="col-lg-6">
              <div class="wisata-card h-100">
                <div class="card-body p-4">
                  <div class="text-center mb-4">
                    <div class="icon-wrapper mb-3">
                      <i class="fas fa-route text-white fa-2x"></i>
                    </div>
                    <h4 class="card-title">Cara Menuju Klabili</h4>
                  </div>

                  <ul class="nav nav-pills nav-fill mb-4" id="transportTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                      <button class="nav-link active rounded-pill" id="udara-tab" data-bs-toggle="tab"
                              data-bs-target="#udara" type="button" role="tab" aria-controls="udara" aria-selected="true">
                        <i class="fas fa-plane me-2"></i>Udara
                      </button>
                    </li>
                    <li class="nav-item" role="presentation">
                      <button class="nav-link rounded-pill" id="darat-tab" data-bs-toggle="tab"
                              data-bs-target="#darat" type="button" role="tab" aria-controls="darat" aria-selected="false">
                        <i class="fas fa-car me-2"></i>Darat
                      </button>
                    </li>
                    <li class="nav-item" role="presentation">
                      <button class="nav-link rounded-pill" id="laut-tab" data-bs-toggle="tab"
                              data-bs-target="#laut" type="button" role="tab" aria-controls="laut" aria-selected="false">
                        <i class="fas fa-ship me-2"></i>Laut
                      </button>
                    </li>
                  </ul>

                  <div class="tab-content" id="transportTabsContent">
                    <div class="tab-pane fade show active" id="udara" role="tabpanel" aria-labelledby="udara-tab">
                      <div class="transport-info bg-light rounded-3 p-3">
                        <div class="d-flex align-items-start">
                          <div class="transport-icon me-3">
                            <i class="fas fa-plane text-primary"></i>
                          </div>
                          <div>
                            <h6 class="fw-bold text-success mb-2">Dari Sorong</h6>
                            <p class="mb-2">Penerbangan ke Bandara Domine Eduard Osok</p>
                            <p class="mb-0 text-muted">
                              <i class="fas fa-clock me-1"></i>
                              Kemudian perjalanan darat ke Klabili melalui Sausapor (~2 jam)
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="tab-pane fade" id="darat" role="tabpanel" aria-labelledby="darat-tab">
                      <div class="transport-info bg-light rounded-3 p-3">
                        <div class="d-flex align-items-start">
                          <div class="transport-icon me-3">
                            <i class="fas fa-car text-primary"></i>
                          </div>
                          <div>
                            <h6 class="fw-bold text-success mb-2">Dari Sorong</h6>
                            <p class="mb-2">Rute: Sorong → Klabili</p>
                            <p class="mb-0 text-muted">
                              <i class="fas fa-clock me-1"></i>
                              Kendaraan 4×4 atau sepeda motor trail (~2 jam)
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="tab-pane fade" id="laut" role="tabpanel" aria-labelledby="laut-tab">
                      <div class="transport-info bg-light rounded-3 p-3">
                        <div class="d-flex align-items-start">
                          <div class="transport-icon me-3">
                            <i class="fas fa-ship text-primary"></i>
                          </div>
                          <div>
                            <h6 class="fw-bold text-success mb-2">Akses Laut</h6>
                            <p class="mb-2">Pelabuhan terdekat: Sausapor (kargo mingguan)</p>
                            <p class="mb-0 text-muted">
                              <i class="fas fa-info-circle me-1"></i>
                              Speedboat Sorong–Sausapor (~2 jam) sedang direncanakan
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Enhanced Map Section -->
          <div class="row">
            <div class="col-12">
              <div class="wisata-card">
                <div class="card-body p-0">
                  <div class="map-header bg-success text-white p-4 text-center">
                    <h4 class="mb-2">
                      <i class="fas fa-map me-2"></i>Peta Lokasi
                    </h4>
                    <p class="mb-0">Visualisasi lokasi Desa Wisata Klabili</p>
                  </div>
                  <div class="map-container-enhanced">
                    <div class="map-placeholder text-center py-5">
                      <div class="map-icon-wrapper mb-4">
                        <i class="fas fa-map-marked-alt fa-4x text-success"></i>
                      </div>
                      <h5 class="text-success mb-3">Peta Interaktif Segera Hadir</h5>
                      <p class="text-muted mb-4">Peta lokasi detail dengan rute perjalanan akan tersedia di sini</p>
                      <div class="map-features d-flex justify-content-center flex-wrap gap-3">
                        <span class="badge bg-light text-dark px-3 py-2">
                          <i class="fas fa-route me-1"></i>Rute Perjalanan
                        </span>
                        <span class="badge bg-light text-dark px-3 py-2">
                          <i class="fas fa-map-pin me-1"></i>Titik Lokasi
                        </span>
                        <span class="badge bg-light text-dark px-3 py-2">
                          <i class="fas fa-compass me-1"></i>Navigasi
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Contact Section -->
      <section id="kontak" class="py-5" style="background: linear-gradient(135deg, #e8f5e8 0%, #f8f9fa 100%);">
        <div class="container">
          <div class="row">
            <div class="col-12 text-center mb-5">
              <div class="section-header">
                <span class="badge bg-primary-subtle text-primary px-3 py-2 rounded-pill mb-3">
                  <i class="fas fa-phone me-2"></i>Hubungi Kami
                </span>
                <h2 class="section-title">Informasi Kontak</h2>
                <p class="section-subtitle">
                  Hubungi kami untuk pemesanan dan pertanyaan seputar Desa Wisata Klabili
                </p>
              </div>
            </div>
          </div>

          <!-- Primary Contact Methods -->
          <div class="row g-4 mb-5">
            <div class="col-lg-4 col-md-6">
              <div class="contact-card-modern">
                <div class="contact-card-header">
                  <div class="contact-icon-modern phone">
                    <i class="fas fa-phone"></i>
                  </div>
                  <h5 class="contact-title">Telepon</h5>
                </div>
                <div class="contact-card-body">
                  <p class="contact-value">+62 822-3695-5445</p>
                  <span class="contact-status available">
                    <i class="fas fa-circle me-1"></i>Tersedia 24/7
                  </span>
                  <div class="contact-action mt-3">
                    <a href="tel:+6282236955445" class="btn btn-outline-success btn-sm rounded-pill">
                      <i class="fas fa-phone me-2"></i>Hubungi Sekarang
                    </a>
                  </div>
                </div>
              </div>
            </div>

            <div class="col-lg-4 col-md-6">
              <div class="contact-card-modern">
                <div class="contact-card-header">
                  <div class="contact-icon-modern whatsapp">
                    <i class="fab fa-whatsapp"></i>
                  </div>
                  <h5 class="contact-title">WhatsApp</h5>
                </div>
                <div class="contact-card-body">
                  <p class="contact-value">+62 822-3695-5445</p>
                  <span class="contact-status fast">
                    <i class="fas fa-bolt me-1"></i>Respons cepat
                  </span>
                  <div class="contact-action mt-3">
                    <a href="https://wa.me/6282236955445?text=Halo%20saya%20mau%20tanya%20tentang%20Desa%20Wisata%20Klabili"
                       target="_blank" class="btn btn-outline-success btn-sm rounded-pill">
                      <i class="fab fa-whatsapp me-2"></i>Chat WhatsApp
                    </a>
                  </div>
                </div>
              </div>
            </div>

            <div class="col-lg-4 col-md-6">
              <div class="contact-card-modern">
                <div class="contact-card-header">
                  <div class="contact-icon-modern email">
                    <i class="fas fa-envelope"></i>
                  </div>
                  <h5 class="contact-title">Email</h5>
                </div>
                <div class="contact-card-body">
                  <p class="contact-value"><EMAIL></p>
                  <span class="contact-status normal">
                    <i class="fas fa-clock me-1"></i>Respons dalam 24 jam
                  </span>
                  <div class="contact-action mt-3">
                    <a href="mailto:<EMAIL>" class="btn btn-outline-success btn-sm rounded-pill">
                      <i class="fas fa-envelope me-2"></i>Kirim Email
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Social Media Section -->
          <div class="row">
            <div class="col-12">
              <div class="social-media-section">
                <div class="text-center mb-4">
                  <h4 class="text-success mb-3">
                    <i class="fas fa-share-alt me-2"></i>Ikuti Media Sosial Kami
                  </h4>
                  <p class="text-muted">Dapatkan update terbaru tentang kegiatan dan acara di Desa Wisata Klabili</p>
                </div>

                <div class="row g-4">
                  <div class="col-lg-6">
                    <div class="social-card instagram">
                      <div class="social-card-content">
                        <div class="social-icon-wrapper">
                          <i class="fab fa-instagram"></i>
                        </div>
                        <div class="social-info">
                          <h6 class="social-platform">Instagram</h6>
                          <p class="social-handle">@klabili_birdwatching</p>
                          <small class="social-description">Foto dan video kegiatan terbaru</small>
                        </div>
                        <div class="social-action">
                          <a href="https://www.instagram.com/klabili_birdwatching/" target="_blank"
                             class="btn btn-instagram btn-sm rounded-pill">
                            <i class="fab fa-instagram me-2"></i>Follow
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="col-lg-6">
                    <div class="social-card facebook">
                      <div class="social-card-content">
                        <div class="social-icon-wrapper">
                          <i class="fab fa-facebook"></i>
                        </div>
                        <div class="social-info">
                          <h6 class="social-platform">Facebook</h6>
                          <p class="social-handle">Klabili Tourism Village</p>
                          <small class="social-description">Pembaruan komunitas dan acara</small>
                        </div>
                        <div class="social-action">
                          <a href="https://www.facebook.com/profile.php?id=100094418988540" target="_blank"
                             class="btn btn-facebook btn-sm rounded-pill">
                            <i class="fab fa-facebook me-2"></i>Like
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Additional Information Section -->
      <section id="tambahan" class="py-5" style="background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);">
        <div class="container">
          <div class="row">
            <div class="col-12 text-center mb-5">
              <div class="section-header">
                <span class="badge bg-warning-subtle text-warning px-3 py-2 rounded-pill mb-3">
                  <i class="fas fa-info-circle me-2"></i>Informasi Penting
                </span>
                <h2 class="section-title">Tips Perjalanan & Informasi</h2>
                <p class="section-subtitle">
                  Informasi penting untuk mempersiapkan kunjungan Anda ke Desa Wisata Klabili
                </p>
              </div>
            </div>
          </div>

          <div class="row g-4">
            <!-- Weather & Climate Card -->
            <div class="col-lg-4 col-md-6">
              <div class="info-card-enhanced weather">
                <div class="info-card-header">
                  <div class="info-icon-wrapper weather-icon">
                    <i class="fas fa-cloud-sun"></i>
                  </div>
                  <h4 class="info-card-title">Cuaca & Iklim</h4>
                </div>
                <div class="info-card-body">
                  <div class="weather-details">
                    <div class="weather-item">
                      <div class="weather-label">
                        <i class="fas fa-thermometer-half me-2"></i>Suhu
                      </div>
                      <div class="weather-value">18-25°C</div>
                    </div>
                    <div class="weather-item">
                      <div class="weather-label">
                        <i class="fas fa-cloud-rain me-2"></i>Musim Hujan
                      </div>
                      <div class="weather-value">Nov - Mar</div>
                    </div>
                    <div class="weather-item">
                      <div class="weather-label">
                        <i class="fas fa-sun me-2"></i>Waktu Terbaik
                      </div>
                      <div class="weather-value">Apr - Okt</div>
                    </div>
                  </div>
                  <div class="weather-note">
                    <i class="fas fa-info-circle me-2"></i>
                    Iklim tropis sejuk dengan udara segar pegunungan
                  </div>
                </div>
              </div>
            </div>

            <!-- Travel Tips Card -->
            <div class="col-lg-4 col-md-6">
              <div class="info-card-enhanced tips">
                <div class="info-card-header">
                  <div class="info-icon-wrapper tips-icon">
                    <i class="fas fa-lightbulb"></i>
                  </div>
                  <h4 class="info-card-title">Tips Perjalanan</h4>
                </div>
                <div class="info-card-body">
                  <div class="tips-list">
                    <div class="tip-item">
                      <div class="tip-icon">
                        <i class="fas fa-tshirt"></i>
                      </div>
                      <div class="tip-content">
                        <strong>Yang Dibawa:</strong>
                        <p>Pakaian hangat, perlengkapan hujan, sepatu hiking, obat anti nyamuk</p>
                      </div>
                    </div>
                    <div class="tip-item">
                      <div class="tip-icon">
                        <i class="fas fa-clock"></i>
                      </div>
                      <div class="tip-content">
                        <strong>Waktu Terbaik:</strong>
                        <p>Pagi untuk birdwatching, sore untuk aktivitas budaya</p>
                      </div>
                    </div>
                    <div class="tip-item">
                      <div class="tip-icon">
                        <i class="fas fa-calendar-alt"></i>
                      </div>
                      <div class="tip-content">
                        <strong>Durasi:</strong>
                        <p>Minimum 2-3 hari untuk pengalaman lengkap</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Emergency Contact Card -->
            <div class="col-lg-4 col-md-6">
              <div class="info-card-enhanced emergency">
                <div class="info-card-header">
                  <div class="info-icon-wrapper emergency-icon">
                    <i class="fas fa-first-aid"></i>
                  </div>
                  <h4 class="info-card-title">Kontak Darurat</h4>
                </div>
                <div class="info-card-body">
                  <div class="emergency-list">
                    <div class="emergency-contact">
                      <div class="emergency-contact-icon">
                        <i class="fas fa-hospital"></i>
                      </div>
                      <div class="emergency-contact-info">
                        <h6>Pos Kesehatan Desa</h6>
                        <p>Layanan medis dasar tersedia di desa</p>
                        <span class="availability">Buka 24 jam</span>
                      </div>
                    </div>
                    <div class="emergency-contact">
                      <div class="emergency-contact-icon">
                        <i class="fas fa-phone-alt"></i>
                      </div>
                      <div class="emergency-contact-info">
                        <h6>Bantuan Darurat</h6>
                        <p class="emergency-number">+62 822-3695-5445</p>
                        <span class="availability">Tersedia 24/7</span>
                      </div>
                    </div>
                  </div>
                  <div class="emergency-note">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Simpan nomor ini di ponsel Anda
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Additional Tips Section -->
          <div class="row mt-5">
            <div class="col-12">
              <div class="additional-tips-section">
                <div class="row g-4">
                  <div class="col-lg-6">
                    <div class="tip-highlight-card">
                      <div class="tip-highlight-header">
                        <i class="fas fa-camera fa-2x text-success mb-3"></i>
                        <h5>Tips Fotografi</h5>
                      </div>
                      <div class="tip-highlight-body">
                        <ul class="tip-list">
                          <li>Golden hour terbaik: 06:00-07:00 dan 17:00-18:00</li>
                          <li>Bawa lensa telephoto untuk foto burung</li>
                          <li>Siapkan pelindung kamera dari hujan</li>
                          <li>Hormati privasi masyarakat lokal</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-6">
                    <div class="tip-highlight-card">
                      <div class="tip-highlight-header">
                        <i class="fas fa-leaf fa-2x text-success mb-3"></i>
                        <h5>Etika Lingkungan</h5>
                      </div>
                      <div class="tip-highlight-body">
                        <ul class="tip-list">
                          <li>Jangan meninggalkan sampah di alam</li>
                          <li>Gunakan jalur yang sudah ada</li>
                          <li>Jangan memetik tanaman atau mengganggu satwa</li>
                          <li>Dukung produk lokal dan ramah lingkungan</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
      <div class="container">
        <div class="row">
          <!-- Column 1: Logo and Description -->
          <div class="col-lg-3 col-md-6 mb-4">
            <div class="mb-3">
              <img
                src="img/LogoDesaWisata/Logo_Desa_Wisata_Klabili-removebg-preview.png"
                alt="Klabili Tourism Village Logo"
                width="150px"
              />
            </div>
            <p class="text-white-50">
              Klabili Tourism Village offers authentic tourism experiences with
              natural beauty, local culture, and the warmth of the Moi Tribe
              community.
            </p>
          </div>

          <!-- Column 2: Quick Links -->
          <div class="col-lg-3 col-md-6 mb-4">
            <h5 class="fw-bold mb-3">Quick Links</h5>
            <ul class="list-unstyled">
              <li class="mb-2">
                <a
                  href="index_en.html"
                  class="text-white-50 text-decoration-none"
                  >Home</a
                >
              </li>
              <li class="mb-2">
                <a
                  href="tentang_kami_en.html"
                  class="text-white-50 text-decoration-none"
                  >About Us</a
                >
              </li>
              <li class="mb-2">
                <a
                  href="fasilitas_en.html"
                  class="text-white-50 text-decoration-none"
                  >Facilities</a
                >
              </li>
              <li class="mb-2">
                <a
                  href="wisata_budaya_en.html"
                  class="text-white-50 text-decoration-none"
                  >Tourism & Culture</a
                >
              </li>
              <li class="mb-2">
                <a
                  href="galeri_en.html"
                  class="text-white-50 text-decoration-none"
                  >Gallery</a
                >
              </li>
            </ul>
          </div>

          <!-- Column 3: Contact Info -->
          <div class="col-lg-3 col-md-6 mb-4">
            <h5 class="fw-bold mb-3">Contact Us</h5>
            <ul class="list-unstyled">
              <li class="mb-2">
                <i class="fas fa-phone me-2"></i>
                <span>+62 822-3695-5445</span>
              </li>
              <li class="mb-2">
                <i class="fas fa-map-marker-alt me-2"></i>
                <span
                  >Klabili Village, Selemkai District, Tambrauw Regency</span
                >
              </li>
              <li class="mb-2">
                <i class="fas fa-envelope me-2"></i>
                <span><EMAIL></span>
              </li>
            </ul>
          </div>

          <!-- Column 4: Social Media -->
          <div class="col-lg-3 col-md-6 mb-4">
            <h5 class="fw-bold mb-3">Ikuti Kami</h5>
            <div class="social-media-links d-flex flex-wrap gap-3">
              <!-- Instagram -->
              <a href="https://www.instagram.com/klabili_birdwatching/" target="_blank" rel="noopener noreferrer"
                class="social-link instagram-link" title="Ikuti kami di Instagram">
                <div class="social-icon">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                  </svg>
                </div>
                <span class="social-label">Instagram</span>
              </a>

              <!-- Facebook -->
              <a href="https://www.facebook.com/profile.php?id=100094418988540" target="_blank" rel="noopener noreferrer"
                class="social-link facebook-link" title="Ikuti kami di Facebook">
                <div class="social-icon">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                  </svg>
                </div>
                <span class="social-label">Facebook</span>
              </a>

              <!-- WhatsApp -->
              <a href="https://wa.me/6282236955445?text=Halo%20saya%20mau%20tanya%20tentang%20Desa%20Wisata%20Klabili" target="_blank"
                rel="noopener noreferrer" class="social-link whatsapp-link" title="Hubungi kami di WhatsApp">
                <div class="social-icon">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.488"/>
                  </svg>
                </div>
                <span class="social-label">WhatsApp</span>
              </a>
            </div>
          </div>
        </div>

        <!-- Copyright -->
        <hr class="my-4" />
        <div class="row">
          <div class="col-12 text-center">
            <p class="mb-0 text-white-50">
              &copy; 2025 Klabili Tourism Village. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </footer>

    <!-- Scroll to Top Button -->
    <button
      id="scrollToTopBtn"
      class="btn btn-success rounded-circle shadow"
      style="
        position: fixed;
        bottom: 32px;
        right: 32px;
        z-index: 999;
        display: none;
        width: 48px;
        height: 48px;
        align-items: center;
        justify-content: center;
      "
    >
      <i class="fas fa-leaf"></i>
    </button>

    <!-- Font Awesome for icons -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />

    <!-- Bootstrap JS -->
    <script src="js/bootstrap/bootstrap.bundle.js"></script>
    <!-- Custom JS -->
    <script src="js/custom.js"></script>

    <script>
      // Show/hide button on scroll
      window.addEventListener("scroll", function () {
        const btn = document.getElementById("scrollToTopBtn");
        if (window.scrollY > 200) {
          btn.classList.add("show");
        } else {
          btn.classList.remove("show");
        }
      });

      // Scroll to top on click
      document.getElementById("scrollToTopBtn").onclick = function () {
        window.scrollTo({ top: 0, behavior: "smooth" });
      };
    </script>
  </body>
</html>
